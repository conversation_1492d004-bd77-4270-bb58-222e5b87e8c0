"use client";

import React from "react";

export interface NotionEmbedProps {
  isExpanded: boolean;
  notionPageUrl?: string;
}

function NotionEmbed({ isExpanded, notionPageUrl }: NotionEmbedProps) {
  // Default Notion page URL if none provided
  const defaultNotionUrl = "https://www.notion.so";
  const embedUrl = notionPageUrl || defaultNotionUrl;

  return (
    <div
      className={
        (isExpanded ? "w-1/2 overflow-auto" : "w-0 overflow-hidden opacity-0") +
        " transition-all rounded-xl duration-200 ease-in-out flex-col bg-white"
      }
    >
      {isExpanded && (
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between px-6 py-3.5 sticky top-0 z-10 text-base border-b bg-white rounded-t-xl">
            <span className="font-semibold">Notion Page</span>
          </div>
          <div className="flex-1 p-4">
            <iframe
              src={embedUrl}
              className="w-full h-full border-0 rounded-lg"
              title="Notion Page"
              allow="fullscreen"
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default NotionEmbed;
